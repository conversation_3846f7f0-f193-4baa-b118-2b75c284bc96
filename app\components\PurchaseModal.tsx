"use client"

import React, { useState, useEffect } from "react"
import { X, HelpCircle, Mail, Wallet, DollarSign, AlertCircle, Eye, EyeOff, Info, CheckCircle, XCircle } from "lucide-react"
import type { Product, Package, CustomField, Dropdown, DropdownOption } from "../types"
import { convertAndFormatPrice } from "../utils/currency"
import { Money } from "./Money"
import { useData } from "../contexts/DataContext"
import { useAuth } from "../contexts/AuthContext"
import { getCurrencySymbol } from "../utils/currency"
import { getProductPrice, type UserRole } from "../utils/pricing"
import { getCustomFields } from "../lib/optimizedQueries"
import { createClient } from "../lib/supabase/client"
import { toast } from "sonner"

interface PurchaseModalProps {
  isOpen: boolean
  onClose: () => void
  product: Product | null
  selectedPackage: Package | null
  quantity: number
  onPurchase: (formData: any) => void
  isStandaloneProduct?: boolean
}

export default function PurchaseModal({
  isOpen,
  onClose,
  product,
  selectedPackage,
  quantity,
  onPurchase,
  isStandaloneProduct = false
}: PurchaseModalProps) {
  const { currentUser } = useAuth()
  const {
    currencies,
    selectedCurrency,
    userBalances,
    convertPrice,
    refreshUserBalances
  } = useData()

  const [formData, setFormData] = useState<Record<string, string>>({})
  const [email, setEmail] = useState("")
  const [emailOptIn, setEmailOptIn] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [paymentMethod, setPaymentMethod] = useState<'wallet' | 'external'>('wallet')
  const [showBalanceWarning, setShowBalanceWarning] = useState(false)

  // Enhanced custom fields state
  const [customFields, setCustomFields] = useState<CustomField[]>([])
  const [dropdownFields, setDropdownFields] = useState<Dropdown[]>([])
  const [fieldValidation, setFieldValidation] = useState<Record<string, { isValid: boolean; message?: string; strength?: 'weak' | 'medium' | 'strong' }>>({})
  const [showPasswords, setShowPasswords] = useState<Record<string, boolean>>({})
  const [fieldsLoading, setFieldsLoading] = useState(false)

  // Calculate values (always, regardless of isOpen) with null checks
  const totalPriceUSD = isStandaloneProduct && product
    ? getProductPrice(product, (currentUser?.role as UserRole) || 'user') * quantity
    : selectedPackage
      ? selectedPackage.price * quantity
      : 0

  const selectedCurrencyData = currencies.find(c => c.code === selectedCurrency)
  const totalPriceInCurrency = selectedCurrencyData
    ? totalPriceUSD * selectedCurrencyData.exchange_rate
    : totalPriceUSD

  const userBalance = userBalances[selectedCurrency] || 0
  const hasInsufficientBalance = paymentMethod === 'wallet' && userBalance < totalPriceInCurrency

  // Check balance when payment method or currency changes (always call useEffect)
  React.useEffect(() => {
    if (isOpen && paymentMethod === 'wallet' && userBalance < totalPriceInCurrency) {
      setShowBalanceWarning(true)
    } else {
      setShowBalanceWarning(false)
    }
  }, [isOpen, paymentMethod, userBalance, totalPriceInCurrency])

  // Load custom fields and dropdowns when modal opens
  useEffect(() => {
    const loadCustomFields = async () => {
      if (!isOpen || !product) return

      setFieldsLoading(true)

      try {
        const supabase = createClient()

        // Get user's tenant
        const { data: { user } } = await supabase.auth.getUser()
        if (!user) return

        const { data: profile } = await supabase
          .from('user_profiles')
          .select('tenant_id')
          .eq('id', user.id)
          .single()

        if (!profile) return

        // Determine what fields to fetch
        const filters: any = { tenantId: profile.tenant_id }

        if (isStandaloneProduct) {
          filters.productId = product.id
        } else if (selectedPackage) {
          filters.packageId = selectedPackage.id
        }

        // Fetch custom fields and dropdowns
        const { customFields, dropdowns } = await getCustomFields(supabase, filters)

        setCustomFields(customFields || [])
        setDropdownFields(dropdowns || [])

        // Set default values for dropdowns
        const newFormData = { ...formData }
        if (dropdowns && dropdowns.length > 0) {
          dropdowns.forEach((dropdown: Dropdown) => {
            if (!newFormData[dropdown.id]) {
              const defaultOption = dropdown.options?.find(opt => opt.is_default)
              if (defaultOption) {
                newFormData[dropdown.id] = defaultOption.label
              }
            }
          })
          setFormData(newFormData)
        }

      } catch (error) {
        console.error('Error loading custom fields:', error)
      } finally {
        setFieldsLoading(false)
      }
    }

    loadCustomFields()
  }, [isOpen, product, selectedPackage, isStandaloneProduct])

  // Early return after all hooks - allow standalone products without packages
  if (!isOpen || !product) return null
  if (!isStandaloneProduct && !selectedPackage) return null

  const hasDiscount = !isStandaloneProduct && selectedPackage?.discount && selectedPackage.discount > 0

  // Validate field value
  const validateField = (field: CustomField, value: string) => {
    const rules = field.validation_rules || {}

    // Required field validation
    if (field.required && (!value || value.trim() === '')) {
      return { isValid: false, message: `${field.label} مطلوب` }
    }

    // Skip validation if field is empty and not required
    if (!value || value.trim() === '') {
      return { isValid: true }
    }

    // Pattern validation
    if (rules.pattern) {
      const regex = new RegExp(rules.pattern)
      if (!regex.test(value)) {
        return { isValid: false, message: `${field.label} غير صحيح` }
      }
    }

    // Length validation
    if (rules.minLength && value.length < rules.minLength) {
      return { isValid: false, message: `${field.label} يجب أن يكون ${rules.minLength} أحرف على الأقل` }
    }
    if (rules.maxLength && value.length > rules.maxLength) {
      return { isValid: false, message: `${field.label} يجب أن يكون ${rules.maxLength} أحرف كحد أقصى` }
    }

    // Email validation
    if (field.field_type === 'email') {
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
      if (!emailRegex.test(value)) {
        return { isValid: false, message: 'البريد الإلكتروني غير صحيح' }
      }
    }

    // Password strength validation
    if (field.field_type === 'password' && field.display_options?.showStrength) {
      const strength = calculatePasswordStrength(value)
      return { isValid: true, strength }
    }

    return { isValid: true }
  }

  // Calculate password strength
  const calculatePasswordStrength = (password: string): 'weak' | 'medium' | 'strong' => {
    let score = 0
    if (password.length >= 8) score++
    if (/[a-z]/.test(password)) score++
    if (/[A-Z]/.test(password)) score++
    if (/[0-9]/.test(password)) score++
    if (/[^a-zA-Z0-9]/.test(password)) score++

    if (score <= 2) return 'weak'
    if (score <= 4) return 'medium'
    return 'strong'
  }

  const handleInputChange = (fieldId: string, value: string, field?: CustomField) => {
    setFormData(prev => ({
      ...prev,
      [fieldId]: value
    }))

    // Validate field if it's a custom field
    if (field) {
      const validation = validateField(field, value)
      setFieldValidation(prev => ({ ...prev, [fieldId]: validation }))
    }
  }

  const handleDropdownChange = (fieldId: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [fieldId]: value
    }))
  }

  // Toggle password visibility
  const togglePasswordVisibility = (fieldId: string) => {
    setShowPasswords(prev => ({ ...prev, [fieldId]: !prev[fieldId] }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Validate required fields
      const missingFields = []

      // Check enhanced custom fields
      for (const field of customFields) {
        if (field.required && !formData[field.id]) {
          missingFields.push(field.label)
        }
      }

      // Check enhanced dropdowns
      for (const dropdown of dropdownFields) {
        if (dropdown.required && !formData[dropdown.id]) {
          missingFields.push(dropdown.label)
        }
      }

      // Check field validation
      for (const field of customFields) {
        const validation = fieldValidation[field.id]
        if (validation && !validation.isValid) {
          missingFields.push(`${field.label} (${validation.message})`)
        }
      }

      if (missingFields.length > 0) {
        toast.error(`يرجى ملء الحقول المطلوبة: ${missingFields.join(', ')}`)
        setIsSubmitting(false)
        return
      }

      // Check balance for wallet payment
      if (paymentMethod === 'wallet' && hasInsufficientBalance) {
        toast.error(`رصيد غير كافي. المطلوب: ${totalPriceInCurrency.toFixed(2)} ${selectedCurrency}`)
        setIsSubmitting(false)
        return
      }

      // Create order via API
      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId: product.id,
          packageId: isStandaloneProduct ? null : selectedPackage?.id,
          quantity,
          customData: {
            ...formData,
            email: emailOptIn ? email : null,
          },
          paymentMethod,
          currencyCode: selectedCurrency,
          isStandaloneProduct
        })
      })

      const result = await response.json()

      if (response.ok) {
        toast.success('تم إنشاء الطلب بنجاح!')

        // Refresh user balances if paid with wallet
        if (paymentMethod === 'wallet') {
          await refreshUserBalances()
        }

        // Call the original onPurchase callback for any additional handling
        await onPurchase({
          ...formData,
          email: emailOptIn ? email : null,
          quantity,
          totalPrice: totalPriceInCurrency,
          orderId: result.order.id,
          paymentMethod
        })

        onClose()
      } else {
        if (result.error === 'Insufficient balance') {
          toast.error(`رصيد غير كافي. المطلوب: ${result.required.toFixed(2)} ${result.currency}، المتاح: ${result.available.toFixed(2)} ${result.currency}`)
        } else {
          toast.error(result.error || 'فشل في إنشاء الطلب')
        }
      }
    } catch (error) {
      console.error('Error creating order:', error)
      toast.error('حدث خطأ في إنشاء الطلب')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="fixed inset-0 z-50 flex items-end sm:items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="relative bg-gray-900 w-full sm:max-w-md sm:mx-4 sm:rounded-xl border border-gray-700/50 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="sticky top-0 bg-gray-900 border-b border-gray-700/50 p-4 flex items-center justify-between">
          <h2 className="text-lg font-bold text-white">معلومات المنتج</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-800 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-400" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-4 space-y-4">
          {/* Enhanced Dynamic Custom Fields */}
          {fieldsLoading ? (
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-4 bg-gray-600 rounded w-1/4 mb-2"></div>
                  <div className="h-10 bg-gray-700 rounded"></div>
                </div>
              ))}
            </div>
          ) : (
            <>
              {/* Combine and sort all fields */}
              {[
                ...customFields.map(field => ({ ...field, type: 'custom' as const })),
                ...dropdownFields.map(field => ({ ...field, type: 'dropdown' as const }))
              ]
                .sort((a, b) => (a.field_order || 0) - (b.field_order || 0))
                .map((field) => {
                  const fieldId = field.id
                  const validation = fieldValidation[fieldId]
                  const isInvalid = validation && !validation.isValid

                  if (field.type === 'dropdown') {
                    const dropdownField = field as Dropdown
                    return (
                      <div key={fieldId} className="space-y-2">
                        <label className="block text-sm font-medium text-white">
                          {dropdownField.label}
                          {dropdownField.required && <span className="text-red-400 mr-1">*</span>}
                        </label>

                        {dropdownField.description && (
                          <p className="text-sm text-gray-300 leading-relaxed">
                            {dropdownField.description}
                          </p>
                        )}

                        <select
                          value={formData[fieldId] || ''}
                          onChange={(e) => handleDropdownChange(fieldId, e.target.value)}
                          className={`w-full bg-gray-800 border rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent ${
                            isInvalid ? 'border-red-500' : 'border-gray-700'
                          }`}
                          required={dropdownField.required}
                        >
                          <option value="">
                            {dropdownField.display_options?.placeholder || `اختر ${dropdownField.label}`}
                          </option>
                          {dropdownField.options?.map((option) => (
                            <option key={option.id} value={option.label}>
                              {option.label}
                            </option>
                          ))}
                        </select>

                        {dropdownField.display_options?.helpText && (
                          <div className="mt-1 flex items-center space-x-1 rtl:space-x-reverse">
                            <Info className="w-4 h-4 text-gray-400" />
                            <span className="text-xs text-gray-400">
                              {dropdownField.display_options.helpText}
                            </span>
                          </div>
                        )}
                      </div>
                    )
                  } else {
                    const customField = field as CustomField
                    const fieldType = customField.field_type || customField.type || 'text'
                    const showPassword = showPasswords[fieldId]

                    return (
                      <div key={fieldId} className="space-y-2">
                        <label className="block text-sm font-medium text-white">
                          {customField.label}
                          {customField.required && <span className="text-red-400 mr-1">*</span>}
                        </label>

                        {customField.description && (
                          <p className="text-sm text-gray-300 leading-relaxed">
                            {customField.description}
                          </p>
                        )}

                        <div className="relative">
                          <input
                            type={fieldType === 'password' && showPassword ? 'text' : fieldType}
                            value={formData[fieldId] || ''}
                            onChange={(e) => handleInputChange(fieldId, e.target.value, customField)}
                            placeholder={customField.placeholder}
                            className={`w-full bg-gray-800 border rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent ${
                              isInvalid ? 'border-red-500' : 'border-gray-700'
                            } ${fieldType === 'password' ? 'pr-12' : ''}`}
                            required={customField.required}
                          />

                          {/* Password visibility toggle */}
                          {fieldType === 'password' && (
                            <button
                              type="button"
                              onClick={() => togglePasswordVisibility(fieldId)}
                              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
                            >
                              {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                            </button>
                          )}

                          {/* Validation icon */}
                          {formData[fieldId] && (
                            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                              {validation?.isValid ? (
                                <CheckCircle className="w-5 h-5 text-green-500" />
                              ) : (
                                <XCircle className="w-5 h-5 text-red-500" />
                              )}
                            </div>
                          )}
                        </div>

                        {/* Validation message */}
                        {isInvalid && validation?.message && (
                          <p className="text-sm text-red-400 flex items-center space-x-1 rtl:space-x-reverse">
                            <AlertCircle className="w-4 h-4" />
                            <span>{validation.message}</span>
                          </p>
                        )}

                        {/* Help text */}
                        {customField.display_options?.helpText && (
                          <div className="flex items-center space-x-1 rtl:space-x-reverse">
                            <Info className="w-4 h-4 text-gray-400" />
                            <span className="text-xs text-gray-400">
                              {customField.display_options.helpText}
                            </span>
                          </div>
                        )}
                      </div>
                    )
                  }
                })}
            </>
          )}

          {/* PS/Xbox Guide Link */}
          <div className="flex items-center space-x-2 space-x-reverse text-purple-400 text-sm">
            <HelpCircle className="w-4 h-4" />
            <span>دليل استرداد لاعب PS/Xbox</span>
          </div>

          {/* Email Opt-in */}
          <div className="bg-gray-800/50 rounded-lg p-4 space-y-3">
            <div className="flex items-start space-x-3 space-x-reverse">
              <Mail className="w-5 h-5 text-purple-400 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <h3 className="font-medium text-white mb-1">
                  تأكيد البريد الإلكتروني لتلقي كوبون خصم 5% والحصول على أحدث العروض في نشرتنا الإخبارية
                </h3>
                <p className="text-sm text-gray-400">
                  يرجى التحقق من بريدك الإلكتروني التأكيدي لتلقي الكوبون الخاص بك
                </p>
              </div>
            </div>
            
            <div className="space-y-2">
              <input
                type="email"
                placeholder="يرجى إدخال البريد الإلكتروني"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
              <label className="flex items-center space-x-2 space-x-reverse">
                <input
                  type="checkbox"
                  checked={emailOptIn}
                  onChange={(e) => setEmailOptIn(e.target.checked)}
                  className="rounded border-gray-600 text-purple-600 focus:ring-purple-500"
                />
                <span className="text-sm text-gray-300">أوافق على تلقي النشرة الإخبارية</span>
              </label>
            </div>
          </div>

          {/* Payment Method Selection */}
          {currentUser && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white">طريقة الدفع</h3>

              <div className="grid grid-cols-1 gap-3">
                {/* Wallet Payment */}
                <button
                  type="button"
                  onClick={() => setPaymentMethod('wallet')}
                  className={`p-4 rounded-lg border-2 transition-all ${
                    paymentMethod === 'wallet'
                      ? 'border-purple-500 bg-purple-500/10'
                      : 'border-gray-600 hover:border-gray-500'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <Wallet className="w-6 h-6 text-purple-400" />
                      <div className="text-right">
                        <div className="text-white font-medium">دفع من المحفظة</div>
                        <div className="text-sm text-gray-400">
                          الرصيد المتاح: {userBalance.toFixed(2)} {getCurrencySymbol(selectedCurrency)}
                        </div>
                      </div>
                    </div>
                    <div className={`w-4 h-4 rounded-full border-2 ${
                      paymentMethod === 'wallet'
                        ? 'border-purple-500 bg-purple-500'
                        : 'border-gray-400'
                    }`} />
                  </div>

                  {/* Balance Warning */}
                  {paymentMethod === 'wallet' && hasInsufficientBalance && (
                    <div className="mt-3 flex items-center space-x-2 space-x-reverse text-red-400 bg-red-500/10 border border-red-500/20 rounded-lg p-3">
                      <AlertCircle className="w-5 h-5" />
                      <span className="text-sm">
                        رصيد غير كافي. المطلوب: {totalPriceInCurrency.toFixed(2)} {selectedCurrency}
                      </span>
                    </div>
                  )}
                </button>

                {/* External Payment */}
                <button
                  type="button"
                  onClick={() => setPaymentMethod('external')}
                  className={`p-4 rounded-lg border-2 transition-all ${
                    paymentMethod === 'external'
                      ? 'border-blue-500 bg-blue-500/10'
                      : 'border-gray-600 hover:border-gray-500'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <DollarSign className="w-6 h-6 text-blue-400" />
                      <div className="text-right">
                        <div className="text-white font-medium">دفع خارجي</div>
                        <div className="text-sm text-gray-400">
                          سيتم التواصل معك لإتمام الدفع
                        </div>
                      </div>
                    </div>
                    <div className={`w-4 h-4 rounded-full border-2 ${
                      paymentMethod === 'external'
                        ? 'border-blue-500 bg-blue-500'
                        : 'border-gray-400'
                    }`} />
                  </div>
                </button>
              </div>
            </div>
          )}

          {/* Order Summary */}
          <div className="bg-gray-800/30 rounded-lg p-4 space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-gray-300">
                {isStandaloneProduct ? product.title : selectedPackage?.name}
              </span>
              <span className="text-white">
                {totalPriceInCurrency.toFixed(2)} {getCurrencySymbol(selectedCurrency)}
              </span>
            </div>
            {quantity > 1 && (
              <div className="flex justify-between items-center text-sm">
                <span className="text-gray-400">الكمية: {quantity}</span>
                <span className="text-gray-400">
                  {isStandaloneProduct
                    ? ((getProductPrice(product, (currentUser?.role as UserRole) || 'user') || 0) * (selectedCurrencyData?.exchange_rate || 1)).toFixed(2)
                    : ((selectedPackage?.price || 0) * (selectedCurrencyData?.exchange_rate || 1)).toFixed(2)
                  } {getCurrencySymbol(selectedCurrency)} × {quantity}
                </span>
              </div>
            )}
            {!isStandaloneProduct && selectedPackage && selectedPackage.discount && selectedPackage.originalPrice && (
              <div className="flex justify-between items-center text-sm">
                <span className="text-green-400">خصم {selectedPackage.discount}%</span>
                <span className="text-green-400">
                  -<Money usdAmount={(selectedPackage.originalPrice - selectedPackage.price) * quantity} />
                </span>
              </div>
            )}
            <div className="border-t border-gray-700 pt-2 flex justify-between items-center font-bold">
              <span className="text-white">المجموع</span>
              <span className="text-purple-400 text-lg">
                {totalPriceInCurrency.toFixed(2)} {getCurrencySymbol(selectedCurrency)}
              </span>
            </div>
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 disabled:from-gray-600 disabled:to-gray-600 text-white font-bold py-4 px-6 rounded-xl transition-all duration-200 hover:shadow-lg hover:shadow-purple-500/25 disabled:cursor-not-allowed"
          >
            {isSubmitting ? "جاري المعالجة..." : "تأكيد الطلب"}
          </button>
        </form>
      </div>
    </div>
  )
}
