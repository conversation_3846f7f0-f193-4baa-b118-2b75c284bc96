# Multi-Currency Earnings Architecture Design

## 🎯 **Executive Summary**

This document outlines the comprehensive multi-currency earnings calculation system designed to address critical financial reporting issues in our e-commerce platform. The solution ensures accurate profit calculations, proper currency conversion timing, and maintains historical data integrity.

## 🚨 **Critical Issues Identified**

### 1. **Earnings Calculation Errors**
- **Current**: $7.97 revenue = $7.97 profit (100% margin) - Mathematically impossible
- **Root Cause**: Missing or zero cost basis in earnings calculations
- **Financial Risk**: Inaccurate profit reporting, potential compliance issues

### 2. **Currency Conversion Timing**
- **Current**: Exchange rates applied at order creation but not locked
- **Root Cause**: No permanent record of exchange rates used for each transaction
- **Financial Risk**: Historical earnings affected by future exchange rate changes

### 3. **Data Integrity Issues**
- **Current**: Cost basis exists but not properly linked to earnings calculations
- **Root Cause**: Package cost data not consistently applied to order calculations
- **Financial Risk**: Profit margins incorrectly calculated

## 🏗️ **Architecture Design**

### **Core Principles**
1. **Immutable Financial Records**: Once an order is completed, its financial data cannot be altered
2. **Exchange Rate Locking**: Exchange rates are locked at transaction completion time
3. **Audit Trail**: Complete financial audit trail for all currency conversions
4. **Base Currency Consistency**: All financial calculations use USD as base currency
5. **Real-time Accuracy**: Current exchange rates for new transactions

### **Database Schema Enhancements**

#### 1. **Enhanced Orders Table**
```sql
ALTER TABLE orders ADD COLUMN IF NOT EXISTS:
-- Financial tracking
base_currency_amount DECIMAL(10,2),           -- Amount in USD (base currency)
original_currency_code VARCHAR(3),            -- Currency used by customer
original_currency_amount DECIMAL(10,2),       -- Amount in customer's currency
exchange_rate_used DECIMAL(10,6),             -- Exchange rate at completion time
exchange_rate_timestamp TIMESTAMP WITH TIME ZONE, -- When rate was applied

-- Cost basis tracking
cost_basis_usd DECIMAL(10,2),                 -- Product/package cost in USD
profit_usd DECIMAL(10,2),                     -- Calculated profit in USD
profit_margin_percent DECIMAL(5,2),           -- Profit margin percentage

-- Financial status
earnings_locked BOOLEAN DEFAULT FALSE,         -- Prevents recalculation
earnings_locked_at TIMESTAMP WITH TIME ZONE,   -- When earnings were locked
```

#### 2. **Exchange Rate History Table**
```sql
CREATE TABLE exchange_rate_history (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  currency_code VARCHAR(3) NOT NULL,
  exchange_rate DECIMAL(10,6) NOT NULL,
  effective_from TIMESTAMP WITH TIME ZONE NOT NULL,
  effective_to TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure no overlapping periods
  CONSTRAINT no_overlapping_rates EXCLUDE USING gist (
    tenant_id WITH =,
    currency_code WITH =,
    tstzrange(effective_from, effective_to, '[)') WITH &&
  )
);
```

#### 3. **Financial Audit Log**
```sql
CREATE TABLE financial_audit_log (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  action VARCHAR(50) NOT NULL, -- 'EARNINGS_CALCULATED', 'EARNINGS_LOCKED', 'CURRENCY_CONVERTED'
  old_values JSONB,
  new_values JSONB,
  exchange_rate_used DECIMAL(10,6),
  performed_by UUID REFERENCES user_profiles(id),
  performed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  INDEX idx_financial_audit_order (tenant_id, order_id, performed_at),
  INDEX idx_financial_audit_action (tenant_id, action, performed_at)
);
```

## 💰 **Earnings Calculation Engine**

### **Multi-Currency Earnings Flow**

```typescript
interface EarningsCalculationInput {
  orderId: string
  productId: string
  packageId: string
  quantity: number
  customerCurrency: string
  customerAmount: number
  completionTimestamp: Date
}

interface EarningsResult {
  baseCurrencyAmount: number      // USD amount
  originalCurrencyAmount: number  // Customer currency amount
  costBasisUSD: number           // Product cost in USD
  profitUSD: number              // Profit in USD
  profitMarginPercent: number    // Profit margin %
  exchangeRateUsed: number       // Exchange rate applied
  exchangeRateTimestamp: Date    // When rate was captured
}
```

### **Calculation Steps**

1. **Capture Exchange Rate**: Get current exchange rate at order completion
2. **Convert to Base Currency**: Convert customer amount to USD using captured rate
3. **Calculate Cost Basis**: Get product/package cost in USD
4. **Calculate Profit**: `profit = revenue_usd - cost_basis_usd`
5. **Lock Financial Data**: Mark earnings as immutable
6. **Audit Trail**: Log all financial calculations

## 🔒 **Financial Data Integrity**

### **Immutability Rules**
1. Once `earnings_locked = TRUE`, financial fields cannot be modified
2. Exchange rates are locked at transaction completion time
3. Historical orders maintain their original exchange rates
4. Cost basis is locked when order is completed

### **Validation Rules**
1. `profit_usd = base_currency_amount - cost_basis_usd`
2. `profit_margin_percent = (profit_usd / base_currency_amount) * 100`
3. `base_currency_amount = original_currency_amount / exchange_rate_used`
4. All financial amounts must be >= 0

## 📊 **Reporting & Analytics**

### **Dashboard Metrics**
```typescript
interface FinancialMetrics {
  totalRevenueUSD: number        // Sum of base_currency_amount
  totalCostUSD: number           // Sum of cost_basis_usd
  totalProfitUSD: number         // Sum of profit_usd
  averageProfitMargin: number    // Weighted average profit margin
  
  // Multi-currency breakdown
  revenueByCurrency: Array<{
    currency: string
    amount: number
    usdEquivalent: number
  }>
  
  // Time-based analysis
  monthlyTrends: Array<{
    month: string
    revenue: number
    profit: number
    margin: number
  }>
}
```

### **Financial Reports**
1. **Profit & Loss Statement**: Revenue, costs, profit by period
2. **Currency Analysis**: Performance by customer currency
3. **Exchange Rate Impact**: How rate changes affect profitability
4. **Cost Basis Analysis**: Product profitability analysis

## 🔄 **Migration Strategy**

### **Phase 1: Schema Updates**
1. Add new financial columns to orders table
2. Create exchange rate history table
3. Create financial audit log table
4. Add necessary indexes

### **Phase 2: Historical Data Migration**
1. Calculate missing cost basis for existing orders
2. Backfill exchange rates from historical data
3. Calculate and lock earnings for completed orders
4. Validate data integrity

### **Phase 3: System Updates**
1. Update order completion process
2. Implement new earnings calculation engine
3. Update dashboard and reporting
4. Add financial audit logging

## 🛡️ **Risk Mitigation**

### **Financial Risks**
1. **Exchange Rate Volatility**: Rates locked at transaction time
2. **Data Corruption**: Immutable financial records with audit trail
3. **Calculation Errors**: Comprehensive validation rules
4. **Compliance Issues**: Complete audit trail for all financial operations

### **Technical Risks**
1. **Performance Impact**: Optimized queries with proper indexing
2. **Data Migration**: Comprehensive testing and rollback procedures
3. **System Downtime**: Phased deployment with minimal disruption

## 📈 **Success Metrics**

### **Financial Accuracy**
- ✅ Profit margins mathematically correct (revenue - cost)
- ✅ Multi-currency conversions accurate and auditable
- ✅ Historical data integrity maintained
- ✅ Real-time financial reporting

### **System Performance**
- ✅ Dashboard load time < 2 seconds
- ✅ Financial calculations < 100ms
- ✅ Exchange rate updates < 1 second
- ✅ Audit trail queries < 500ms

## 🎯 **Implementation Timeline**

### **Week 1: Foundation**
- Database schema updates
- Exchange rate history implementation
- Financial audit logging

### **Week 2: Core Engine**
- Earnings calculation engine
- Currency conversion service
- Data validation framework

### **Week 3: Migration**
- Historical data migration
- Data integrity validation
- Performance optimization

### **Week 4: Integration**
- Dashboard updates
- API enhancements
- Testing and validation

---

**Next Steps**: Proceed with database schema implementation and earnings calculation engine development.
