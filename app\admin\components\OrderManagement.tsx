import { useState, useEffect, useCallback, useMemo } from "react"
import { Search, Filter, Eye, Calendar, Package, Clock, CheckCircle, XCircle, User, DollarSign, Check, X, ChevronLeft, ChevronRight, MoreHorizontal, ChevronsLeft, ChevronsRight, Co<PERSON> } from "lucide-react"
import { toast } from "sonner"
import { getCurrencySymbol } from "../../utils/currency"
import { useAuth } from "../../contexts/AuthContext"

interface OrderWithDetails {
  id: string
  amount: number
  status: 'pending' | 'completed' | 'failed'
  custom_data: Record<string, any>
  created_at: string
  updated_at: string
  user_id: string
  worker_id?: string
  worker_action?: 'accepted' | 'rejected'
  worker_action_at?: string
  products: {
    id: string
    title: string
    slug: string
    cover_image: string
  }
  packages: {
    id: string
    name: string
    price: number
    image: string
  }
  user_profiles?: {
    id: string
    name: string
    email: string
  }
  worker_profiles?: {
    id: string
    name: string
    email: string
    role: string
  }
}

interface PaginationData {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

interface OrdersCache {
  [key: string]: {
    orders: OrderWithDetails[]
    pagination: PaginationData
    timestamp: number
  }
}

export default function OrderManagement() {
  const { authState } = useAuth()
  const [orders, setOrders] = useState<OrderWithDetails[]>([])
  const [loading, setLoading] = useState(true)
  const [actionLoading, setActionLoading] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<"all" | "pending" | "completed" | "failed">(
    authState.user?.role === "worker" ? "pending" : "all"
  )
  const [dateFilter, setDateFilter] = useState<"all" | "today" | "week" | "month">("all")
  const [selectedOrder, setSelectedOrder] = useState<OrderWithDetails | null>(null)
  const [showOrderModal, setShowOrderModal] = useState(false)
  const [pagination, setPagination] = useState<PaginationData>({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  })
  const [ordersCache, setOrdersCache] = useState<OrdersCache>({})
  
  const isWorker = authState.user?.role === "worker"
  const isAdmin = authState.user?.role === "admin"

  // Copy to clipboard function
  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text)
      toast.success(`تم نسخ ${label} بنجاح`)
    } catch (error) {
      console.error('Failed to copy:', error)
      toast.error(`فشل في نسخ ${label}`)
    }
  }

  // Cache key for current filters
  const cacheKey = useMemo(() =>
    `${statusFilter}-${dateFilter}-${searchTerm}-${pagination.page}`,
    [statusFilter, dateFilter, searchTerm, pagination.page]
  )

  // Fetch orders from API with caching and pagination
  const fetchOrders = useCallback(async (page: number = 1, useCache: boolean = true) => {
    const currentCacheKey = `${statusFilter}-${dateFilter}-${searchTerm}-${page}`
    
    // Check cache first (valid for 2 minutes)
    if (useCache && ordersCache[currentCacheKey]) {
      const cached = ordersCache[currentCacheKey]
      const isExpired = Date.now() - cached.timestamp > 2 * 60 * 1000 // 2 minutes
      
      if (!isExpired) {
        setOrders(cached.orders)
        setPagination(cached.pagination)
        setLoading(false)
        return
      }
    }

    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString(),
        ...(statusFilter !== 'all' && { status: statusFilter }),
        ...(searchTerm && { search: searchTerm }),
        ...(dateFilter !== 'all' && { date: dateFilter })
      })
      
      const response = await fetch(`/api/orders?${params}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch orders')
      }
      
      const data = await response.json()
      const newOrders = data.orders || []
      const newPagination = data.pagination || pagination
      
      setOrders(newOrders)
      setPagination(newPagination)
      
      // Update cache
      setOrdersCache(prev => ({
        ...prev,
        [currentCacheKey]: {
          orders: newOrders,
          pagination: newPagination,
          timestamp: Date.now()
        }
      }))
      
    } catch (error) {
      console.error('Error fetching orders:', error)
      toast.error('فشل في تحميل الطلبات')
      setOrders([])
    } finally {
      setLoading(false)
    }
  }, [statusFilter, dateFilter, searchTerm, pagination.limit, ordersCache])

  // Pagination functions
  const goToPage = useCallback((page: number) => {
    if (page >= 1 && page <= pagination.totalPages) {
      setPagination(prev => ({ ...prev, page }))
      fetchOrders(page, true)
    }
  }, [pagination.totalPages, fetchOrders])

  const nextPage = useCallback(() => {
    if (pagination.hasNext) {
      goToPage(pagination.page + 1)
    }
  }, [pagination.hasNext, pagination.page, goToPage])

  const prevPage = useCallback(() => {
    if (pagination.hasPrev) {
      goToPage(pagination.page - 1)
    }
  }, [pagination.hasPrev, pagination.page, goToPage])

  // Handle order action (accept/reject) - for both admins and workers
  const handleOrderAction = async (orderId: string, action: 'accept' | 'reject', reason?: string) => {
    if (!isAdmin && !isWorker) return

    try {
      setActionLoading(orderId)

      const response = await fetch(`/api/orders/${orderId}/worker-action`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action, reason }),
      })

      if (!response.ok) {
        throw new Error('Failed to update order')
      }

      const result = await response.json()
      
      if (result.success) {
        toast.success(action === 'accept' ? 'تم قبول الطلب بنجاح' : 'تم رفض الطلب بنجاح')
        // Clear cache and refresh current page
        setOrdersCache({})
        fetchOrders(pagination.page, false)
      } else {
        throw new Error(result.error || 'Failed to update order')
      }
    } catch (error) {
      console.error('Error updating order:', error)
      toast.error('فشل في تحديث الطلب')
    } finally {
      setActionLoading(null)
    }
  }

  // Order details modal functions
  const openOrderModal = useCallback((order: OrderWithDetails) => {
    setSelectedOrder(order)
    setShowOrderModal(true)
  }, [])

  const closeOrderModal = useCallback(() => {
    setSelectedOrder(null)
    setShowOrderModal(false)
  }, [])

  // Load orders when filters change
  useEffect(() => {
    fetchOrders(1, true)
  }, [statusFilter, dateFilter, searchTerm, fetchOrders])

  // Load orders on component mount
  useEffect(() => {
    fetchOrders(1, true)
  }, [fetchOrders])

  // Status styling helpers
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-yellow-400 bg-yellow-400/20'
      case 'completed': return 'text-green-400 bg-green-400/20'
      case 'failed': return 'text-red-400 bg-red-400/20'
      default: return 'text-gray-400 bg-gray-400/20'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'قيد الانتظار'
      case 'completed': return 'مكتمل'
      case 'failed': return 'فاشل'
      default: return status
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4" />
      case 'pending':
        return <Clock className="w-4 h-4" />
      case 'failed':
        return <XCircle className="w-4 h-4" />
      default:
        return <Clock className="w-4 h-4" />
    }
  }

  // Calculate stats
  const stats = {
    total: pagination.total,
    completed: orders.filter(o => o.status === 'completed').length,
    pending: orders.filter(o => o.status === 'pending').length,
    failed: orders.filter(o => o.status === 'failed').length,
  }

  return (
    <div className="space-y-4 md:space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl md:text-2xl font-bold">إدارة الطلبات</h2>
        <div className="text-sm text-gray-400">
          صفحة {pagination.page} من {pagination.totalPages} ({pagination.total} طلب)
        </div>
      </div>

      {/* Compact Stats Cards */}
      <div className="grid grid-cols-4 gap-2 md:gap-4">
        <div className="bg-gray-800/50 backdrop-blur-md rounded-lg p-2 md:p-4 border border-gray-700/50">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div className="text-center md:text-right">
              <p className="text-gray-400 text-xs md:text-sm">إجمالي</p>
              <p className="text-lg md:text-2xl font-bold">{stats.total}</p>
            </div>
            <Package className="w-4 h-4 md:w-8 md:h-8 text-blue-400 mx-auto md:mx-0 mt-1 md:mt-0" />
          </div>
        </div>

        <div className="bg-gray-800/50 backdrop-blur-md rounded-lg p-2 md:p-4 border border-gray-700/50">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div className="text-center md:text-right">
              <p className="text-gray-400 text-xs md:text-sm">مكتملة</p>
              <p className="text-lg md:text-2xl font-bold text-green-400">{stats.completed}</p>
            </div>
            <CheckCircle className="w-4 h-4 md:w-8 md:h-8 text-green-400 mx-auto md:mx-0 mt-1 md:mt-0" />
          </div>
        </div>

        <div className="bg-gray-800/50 backdrop-blur-md rounded-lg p-2 md:p-4 border border-gray-700/50">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div className="text-center md:text-right">
              <p className="text-gray-400 text-xs md:text-sm">انتظار</p>
              <p className="text-lg md:text-2xl font-bold text-yellow-400">{stats.pending}</p>
            </div>
            <Clock className="w-4 h-4 md:w-8 md:h-8 text-yellow-400 mx-auto md:mx-0 mt-1 md:mt-0" />
          </div>
        </div>

        <div className="bg-gray-800/50 backdrop-blur-md rounded-lg p-2 md:p-4 border border-gray-700/50">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div className="text-center md:text-right">
              <p className="text-gray-400 text-xs md:text-sm">فاشلة</p>
              <p className="text-lg md:text-2xl font-bold text-red-400">{stats.failed}</p>
            </div>
            <XCircle className="w-4 h-4 md:w-8 md:h-8 text-red-400 mx-auto md:mx-0 mt-1 md:mt-0" />
          </div>
        </div>
      </div>

      {/* Compact Filters */}
      <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-3 md:p-6 border border-gray-700/50">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-2 md:gap-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="البحث في الطلبات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-10 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>

          {/* Status Filter */}
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as any)}
            className="bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          >
            <option value="all">جميع الحالات</option>
            <option value="pending">قيد الانتظار</option>
            <option value="completed">مكتملة</option>
            <option value="failed">فاشلة</option>
          </select>

          {/* Date Filter */}
          <select
            value={dateFilter}
            onChange={(e) => setDateFilter(e.target.value as any)}
            className="bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          >
            <option value="all">جميع التواريخ</option>
            <option value="today">اليوم</option>
            <option value="week">هذا الأسبوع</option>
            <option value="month">هذا الشهر</option>
          </select>

          {/* Results Count */}
          <div className="flex items-center justify-center bg-gray-700/30 rounded-lg px-2 md:px-4 py-2 md:py-3">
            <Package className="w-4 h-4 md:w-5 md:h-5 text-purple-400 ml-2" />
            <span className="text-white font-semibold text-sm md:text-base">{orders.length} طلب</span>
          </div>
        </div>
      </div>

      {/* Orders List */}
      <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
            <span className="mr-3 text-gray-400">جاري تحميل الطلبات...</span>
          </div>
        ) : orders.length > 0 ? (
          <>
            {/* Mobile Card View */}
            <div className="md:hidden space-y-3 p-4">
              {orders.map((order) => (
                <div key={order.id} className="bg-gray-700/30 rounded-lg p-4 border border-gray-600/30">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-sm font-mono text-gray-300">#{order.id.slice(0, 8)}</span>
                        <span className={`inline-flex items-center gap-1 px-2 py-1 rounded text-xs ${getStatusColor(order.status)}`}>
                          {getStatusIcon(order.status)}
                          {getStatusText(order.status)}
                        </span>
                      </div>
                      <h3 className="font-medium text-white">{order.products?.title || order.packages?.name}</h3>
                      <p className="text-sm text-gray-400">{order.packages?.name && `${order.packages.name} - `}{order.custom_data?.quantity || 1} قطعة</p>
                      {/* Worker info in mobile view */}
                      {order.worker_action && order.worker_profiles && (
                        <div className="mt-1 text-xs text-gray-400">
                          <span className={`inline-flex items-center gap-1 px-2 py-0.5 rounded ${
                            order.worker_action === 'accepted' ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
                          }`}>
                            {order.worker_action === 'accepted' ? <Check className="w-2 h-2" /> : <X className="w-2 h-2" />}
                            {order.worker_action === 'accepted' ? 'مقبول' : 'مرفوض'}
                          </span>
                          <span className="mr-2">بواسطة: {order.worker_profiles.name}</span>
                        </div>
                      )}
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-white">{order.amount.toFixed(2)} {getCurrencySymbol('USD')}</p>
                      <p className="text-xs text-gray-400">{new Date(order.created_at).toLocaleDateString("en-US", { year: 'numeric', month: '2-digit', day: '2-digit' })}</p>
                    </div>
                  </div>

                  {/* Mobile Action Buttons */}
                  <div className="flex items-center justify-between pt-3 border-t border-gray-600/30">
                    <button
                      onClick={() => openOrderModal(order)}
                      className="flex items-center gap-1 px-3 py-1 bg-blue-600/20 text-blue-400 rounded text-sm hover:bg-blue-600/30 transition-colors"
                    >
                      <Eye className="w-3 h-3" />
                      تفاصيل
                    </button>

                    {(isAdmin || isWorker) && order.status === 'pending' && !order.worker_id && (
                      <div className="flex gap-2">
                        <button
                          onClick={() => handleOrderAction(order.id, 'accept')}
                          disabled={actionLoading === order.id}
                          className="flex items-center gap-1 px-3 py-1 bg-green-600/20 text-green-400 rounded text-sm hover:bg-green-600/30 transition-colors disabled:opacity-50"
                        >
                          {actionLoading === order.id ? (
                            <div className="animate-spin rounded-full h-3 w-3 border-b border-green-400"></div>
                          ) : (
                            <Check className="w-3 h-3" />
                          )}
                          قبول
                        </button>
                        <button
                          onClick={() => handleOrderAction(order.id, 'reject')}
                          disabled={actionLoading === order.id}
                          className="flex items-center gap-1 px-3 py-1 bg-red-600/20 text-red-400 rounded text-sm hover:bg-red-600/30 transition-colors disabled:opacity-50"
                        >
                          {actionLoading === order.id ? (
                            <div className="animate-spin rounded-full h-3 w-3 border-b border-red-400"></div>
                          ) : (
                            <X className="w-3 h-3" />
                          )}
                          رفض
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* Desktop Table View */}
            <div className="hidden md:block overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="text-right text-gray-400 text-sm border-b border-gray-700/50">
                    <th className="p-4">رقم الطلب</th>
                    <th className="p-4">المنتج</th>
                    <th className="p-4">العميل</th>
                    <th className="p-4">المبلغ</th>
                    <th className="p-4">الحالة</th>
                    <th className="p-4">الموظف</th>
                    <th className="p-4">التاريخ</th>
                    <th className="p-4">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {orders.map((order) => (
                    <tr key={order.id} className="border-b border-gray-700/50 hover:bg-gray-700/20">
                      <td className="p-4">
                        <span className="font-mono text-sm">#{order.id.slice(0, 8)}</span>
                      </td>
                      <td className="p-4">
                        <div>
                          <div className="font-semibold">{order.products?.title}</div>
                          <div className="text-sm text-gray-400">{order.packages?.name}</div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div>
                          <div className="font-medium">{order.user_profiles?.name}</div>
                          <div className="text-sm text-gray-400">{order.user_profiles?.email}</div>
                        </div>
                      </td>
                      <td className="p-4">
                        <span className="font-bold">{order.amount.toFixed(2)} {getCurrencySymbol('USD')}</span>
                      </td>
                      <td className="p-4">
                        <span className={`inline-flex items-center gap-1 px-2 py-1 rounded text-xs ${getStatusColor(order.status)}`}>
                          {getStatusIcon(order.status)}
                          {getStatusText(order.status)}
                        </span>
                      </td>
                      <td className="p-4">
                        {order.worker_profiles ? (
                          <div>
                            <div className="font-medium text-sm">{order.worker_profiles.name}</div>
                            <div className="flex items-center gap-1 text-xs text-gray-400">
                              <span>{order.worker_profiles.email}</span>
                              <button
                                onClick={() => copyToClipboard(order.worker_profiles!.email, 'بريد الموظف')}
                                className="p-0.5 text-gray-400 hover:text-white hover:bg-gray-600/30 rounded transition-colors"
                                title="نسخ البريد الإلكتروني"
                              >
                                <Copy className="w-2 h-2" />
                              </button>
                            </div>
                            {order.worker_action && (
                              <span className={`inline-flex items-center gap-1 px-1 py-0.5 rounded text-xs mt-1 ${
                                order.worker_action === 'accepted' ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
                              }`}>
                                {order.worker_action === 'accepted' ? <Check className="w-2 h-2" /> : <X className="w-2 h-2" />}
                                {order.worker_action === 'accepted' ? 'مقبول' : 'مرفوض'}
                              </span>
                            )}
                          </div>
                        ) : order.custom_data?.worker_name ? (
                          <div className="text-sm text-gray-400">{order.custom_data.worker_name}</div>
                        ) : (
                          <span className="text-xs text-gray-500">-</span>
                        )}
                      </td>
                      <td className="p-4">
                        <span className="text-sm text-gray-400">
                          {new Date(order.created_at).toLocaleDateString("en-US", {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit'
                          })}
                        </span>
                      </td>
                      <td className="p-4">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => openOrderModal(order)}
                            className="p-2 text-gray-400 hover:text-blue-400 transition-colors rounded-lg hover:bg-blue-400/10"
                            title="عرض التفاصيل"
                          >
                            <Eye className="w-4 h-4" />
                          </button>

                          {(isAdmin || isWorker) && order.status === 'pending' && !order.worker_id && (
                            <>
                              <button
                                onClick={() => handleOrderAction(order.id, 'accept')}
                                disabled={actionLoading === order.id}
                                className="px-3 py-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white text-sm rounded-lg transition-colors flex items-center gap-1"
                                title="قبول الطلب"
                              >
                                {actionLoading === order.id ? (
                                  <div className="animate-spin rounded-full h-3 w-3 border-b border-white"></div>
                                ) : (
                                  <Check className="w-3 h-3" />
                                )}
                                <span>قبول</span>
                              </button>
                              <button
                                onClick={() => handleOrderAction(order.id, 'reject')}
                                disabled={actionLoading === order.id}
                                className="px-3 py-1 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 text-white text-sm rounded-lg transition-colors flex items-center gap-1"
                                title="رفض الطلب"
                              >
                                {actionLoading === order.id ? (
                                  <div className="animate-spin rounded-full h-3 w-3 border-b border-white"></div>
                                ) : (
                                  <X className="w-3 h-3" />
                                )}
                                <span>رفض</span>
                              </button>
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </>
        ) : (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📦</div>
            <h3 className="text-xl font-semibold mb-2">لا توجد طلبات</h3>
            <p className="text-gray-400">لم يتم العثور على طلبات تطابق المعايير المحددة</p>
          </div>
        )}

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="border-t border-gray-700/50 p-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-400">
                عرض {((pagination.page - 1) * pagination.limit) + 1} إلى {Math.min(pagination.page * pagination.limit, pagination.total)} من {pagination.total} طلب
              </div>

              <div className="flex items-center gap-2">
                <button
                  onClick={() => goToPage(1)}
                  disabled={pagination.page === 1}
                  className="p-2 text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
                  title="الصفحة الأولى"
                >
                  <ChevronsRight className="w-4 h-4" />
                </button>

                <button
                  onClick={prevPage}
                  disabled={!pagination.hasPrev}
                  className="p-2 text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
                  title="الصفحة السابقة"
                >
                  <ChevronRight className="w-4 h-4" />
                </button>

                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                    const pageNum = Math.max(1, Math.min(pagination.totalPages - 4, pagination.page - 2)) + i
                    return (
                      <button
                        key={pageNum}
                        onClick={() => goToPage(pageNum)}
                        className={`px-3 py-1 rounded text-sm ${
                          pageNum === pagination.page
                            ? 'bg-purple-600 text-white'
                            : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
                        }`}
                      >
                        {pageNum}
                      </button>
                    )
                  })}
                </div>

                <button
                  onClick={nextPage}
                  disabled={!pagination.hasNext}
                  className="p-2 text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
                  title="الصفحة التالية"
                >
                  <ChevronLeft className="w-4 h-4" />
                </button>

                <button
                  onClick={() => goToPage(pagination.totalPages)}
                  disabled={pagination.page === pagination.totalPages}
                  className="p-2 text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
                  title="الصفحة الأخيرة"
                >
                  <ChevronsLeft className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Order Details Modal */}
      {showOrderModal && selectedOrder && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gray-800 rounded-xl border border-gray-700 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold">تفاصيل الطلب #{selectedOrder.id.slice(0, 8)}</h3>
                <button
                  onClick={closeOrderModal}
                  className="p-2 text-gray-400 hover:text-white rounded-lg hover:bg-gray-700/50"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              <div className="space-y-6">
                {/* Order Status */}
                <div className="flex items-center gap-3">
                  <span className={`inline-flex items-center gap-2 px-3 py-2 rounded-lg ${getStatusColor(selectedOrder.status)}`}>
                    {getStatusIcon(selectedOrder.status)}
                    {getStatusText(selectedOrder.status)}
                  </span>
                  <span className="text-gray-400">
                    {new Date(selectedOrder.created_at).toLocaleDateString("en-US", {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </span>
                </div>

                {/* Product Details */}
                <div className="bg-gray-700/30 rounded-lg p-4">
                  <h4 className="font-semibold mb-3">تفاصيل المنتج</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-gray-400 text-sm">المنتج</p>
                      <p className="font-medium">{selectedOrder.products?.title}</p>
                    </div>
                    <div>
                      <p className="text-gray-400 text-sm">الحزمة</p>
                      <p className="font-medium">{selectedOrder.packages?.name}</p>
                    </div>
                    <div>
                      <p className="text-gray-400 text-sm">الكمية</p>
                      <p className="font-medium">{selectedOrder.custom_data?.quantity || 1}</p>
                    </div>
                    <div>
                      <p className="text-gray-400 text-sm">المبلغ</p>
                      <p className="font-medium">{selectedOrder.amount.toFixed(2)} {getCurrencySymbol('USD')}</p>
                    </div>
                  </div>
                </div>

                {/* Customer Details */}
                {selectedOrder.user_profiles && (
                  <div className="bg-gray-700/30 rounded-lg p-4">
                    <h4 className="font-semibold mb-3">تفاصيل العميل</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <p className="text-gray-400 text-sm">الاسم</p>
                        <p className="font-medium">{selectedOrder.user_profiles.name}</p>
                      </div>
                      <div>
                        <p className="text-gray-400 text-sm">البريد الإلكتروني</p>
                        <p className="font-medium">{selectedOrder.user_profiles.email}</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Worker Action */}
                {selectedOrder.worker_action && selectedOrder.worker_action_at && (
                  <div className="bg-gray-700/30 rounded-lg p-4">
                    <h4 className="font-semibold mb-3">إجراء الموظف</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                      <div>
                        <p className="text-gray-400 text-sm">الإجراء</p>
                        <span className={`inline-flex items-center gap-2 px-3 py-2 rounded-lg ${
                          selectedOrder.worker_action === 'accepted' ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
                        }`}>
                          {selectedOrder.worker_action === 'accepted' ? <Check className="w-4 h-4" /> : <X className="w-4 h-4" />}
                          {selectedOrder.worker_action === 'accepted' ? 'مقبول' : 'مرفوض'}
                        </span>
                      </div>
                      <div>
                        <p className="text-gray-400 text-sm">التاريخ والوقت</p>
                        <p className="font-medium">
                          {new Date(selectedOrder.worker_action_at).toLocaleDateString("en-US", {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </p>
                      </div>
                      {selectedOrder.worker_profiles && (
                        <>
                          <div>
                            <p className="text-gray-400 text-sm">اسم الموظف</p>
                            <div className="flex items-center gap-2">
                              <p className="font-medium">{selectedOrder.worker_profiles.name}</p>
                              <button
                                onClick={() => copyToClipboard(selectedOrder.worker_profiles!.name, 'اسم الموظف')}
                                className="p-1 text-gray-400 hover:text-white hover:bg-gray-600/30 rounded transition-colors"
                                title="نسخ اسم الموظف"
                              >
                                <Copy className="w-3 h-3" />
                              </button>
                            </div>
                          </div>
                          <div>
                            <p className="text-gray-400 text-sm">بريد الموظف</p>
                            <div className="flex items-center gap-2">
                              <p className="font-medium">{selectedOrder.worker_profiles.email}</p>
                              <button
                                onClick={() => copyToClipboard(selectedOrder.worker_profiles!.email, 'بريد الموظف')}
                                className="p-1 text-gray-400 hover:text-white hover:bg-gray-600/30 rounded transition-colors"
                                title="نسخ البريد الإلكتروني"
                              >
                                <Copy className="w-3 h-3" />
                              </button>
                            </div>
                          </div>
                        </>
                      )}
                      {!selectedOrder.worker_profiles && selectedOrder.custom_data?.worker_name && (
                        <div>
                          <p className="text-gray-400 text-sm">اسم الموظف</p>
                          <p className="font-medium">{selectedOrder.custom_data.worker_name}</p>
                        </div>
                      )}
                    </div>
                    {selectedOrder.custom_data?.worker_reason && (
                      <div className="mt-3 p-3 bg-gray-600/30 rounded-lg">
                        <p className="text-sm text-gray-300">
                          <span className="font-medium">السبب: </span>
                          {selectedOrder.custom_data.worker_reason}
                        </p>
                      </div>
                    )}
                  </div>
                )}

                {/* Action Buttons in Modal */}
                {(isAdmin || isWorker) && selectedOrder.status === 'pending' && !selectedOrder.worker_id && (
                  <div className="flex gap-3 pt-4 border-t border-gray-700">
                    <button
                      onClick={() => {
                        handleOrderAction(selectedOrder.id, 'accept')
                        closeOrderModal()
                      }}
                      disabled={actionLoading === selectedOrder.id}
                      className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg transition-colors"
                    >
                      {actionLoading === selectedOrder.id ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b border-white"></div>
                      ) : (
                        <Check className="w-4 h-4" />
                      )}
                      قبول الطلب
                    </button>
                    <button
                      onClick={() => {
                        handleOrderAction(selectedOrder.id, 'reject')
                        closeOrderModal()
                      }}
                      disabled={actionLoading === selectedOrder.id}
                      className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 text-white rounded-lg transition-colors"
                    >
                      {actionLoading === selectedOrder.id ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b border-white"></div>
                      ) : (
                        <X className="w-4 h-4" />
                      )}
                      رفض الطلب
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
